
## Basic Usage
### 1. Add Bank Accounts to User Model
   Add the ``HasBankAccounts`` trait to any model that should have bank accounts:

```php

use Modules\BankAccounts\Traits\HasBankAccounts;

class User extends Authenticatable
{
use HasBankAccounts;

    // ... rest of your model
}
````
This trait provides the following methods:

bankAccounts() - Returns bank accounts relationship
primaryBankAccount() - Returns primary account

### 2. Using Bank Account Form Components
Dropdown Field
To add a bank account dropdown field in your forms:

```php
use Modules\BankAccounts\Helpers\BankAccountHelper;

// In your Filament form
public static function form(Form $form): Form
{
    return $form
        ->schema([
            BankAccountHelper::bankAccountField(
                fieldName: 'bank_account_id',
                bankableId: $userId,      // ID of the model owning bank accounts
                bankableType: User::class // Class of the model owning bank accounts
            ),
            // other fields...
        ]);
}
```
### Full Bank Account Form
To include a complete bank account form:

```php
use Modules\BankAccounts\Forms\BankAccountForm;

// In your Filament resource/form
public static function form(Form $form): Form
{
    return $form
        ->schema([
            BankAccountForm::make(),
            // other fields...
        ]);
}
```

### 3. Bank Account Model Relationships

```php 
// Get user's bank accounts
$user->bankAccounts;

// Get bank account owner
$bankAccount->bankable;
```

#### Define your related tables in config.php:

The module automatically prevents deletion of bank accounts that are referenced in other tables.

```php

return [
    // ... other configs
    
    'tables' => [
        'leases',
        'property',
        'invoices',
        // Add any other tables that might reference bank accounts
    ],
];
```

