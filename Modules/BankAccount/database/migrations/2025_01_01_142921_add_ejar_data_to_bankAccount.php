<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bank_accounts', function (Blueprint $table) {
            $table->string('bank_address')->nullable()->after('iban');
            $table->string('ejar_id')->nullable()->after('bank_address');
            $table->boolean('ejar_legal_status')->default(false)->after('ejar_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bank_accounts', function (Blueprint $table) {
            $table->dropColumn('bank_address');
            $table->dropColumn('ejar_id');
            $table->dropColumn('ejar_legal_status');
        });
    }
};
