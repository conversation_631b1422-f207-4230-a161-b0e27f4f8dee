<?php
namespace Modules\EjarIntegration\Interfaces;

use App\Models\User;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\EjarHttpHelper;
use Modules\EjarIntegration\Traits\HasCompanyKeys;
abstract class RetrieveLeasesAbstract
{
    use <PERSON>jar<PERSON>ttpHelper, HasCompanyKeys;

    protected string $base_url;
    protected string $clientId;
    protected string $clientSecret;

    protected array $response = ['status' => true, 'message' => null, 'data' => []];

    /**
     * @throws \Exception
     */
    public function __construct(Company $company)
    {
        $this->base_url = "https://test.kera.sa/nhc/uat/v1/ejar/ecrs/";
        $this->setCompanyKeysByCompany($company);
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1"
        ]);
    }
}
