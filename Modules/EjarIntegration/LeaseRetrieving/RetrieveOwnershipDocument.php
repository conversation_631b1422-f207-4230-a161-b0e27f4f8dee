<?php
namespace Modules\EjarIntegration\PropertyRetrieving;

use Modules\EjarIntegration\Interfaces\RetrievePropertiesAbstract;

Class RetrieveOwnershipDocument extends RetrievePropertiesAbstract
{
    public array $ownershipDocument = [];

    /**
     * @throws \Exception
     */
    public function retrieveDocumentDetails(int $documentId): void
    {
        try {
            $url = $this->base_url . 'GetOwnershipDocumentDetail';

            $this->get($url, ["DocumentId" => $documentId]);
            $res = json_decode($this->httpResponse['data'], true);

            if ($this->httpResponse['code'] == 200) {
                $this->ownershipDocument = $res['Body']['data'];
            } else {
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $msg = __("Something went wrong when retrieving document details.");
                }
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
