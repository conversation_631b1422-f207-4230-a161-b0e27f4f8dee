<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use GeniusTS\HijriDate\Hijri;
use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Mo<PERSON>les\Lease\Enums\LeaseTypesEnum;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertySyncStatus;

Class SyncProperty extends SyncStepAbstract implements ISyncInterface
{
    public function sync(): array
    {
        try {
            if ($this->step->status == PropertySyncStatus::COMPLETED || $this->step->status == PropertySyncStatus::NEED_SYNC) {
                $this->response['message'] = __("Property has been synchronized with ejar");
                return $this->response;
            }
            $url = $this->base_url . 'PostProperty';
            $requestData = $this->preparePostPropertyData($this->property);
            $this->post($url, $requestData);
            $res = json_decode($this->httpResponse['data'], true);
            //dd(['url'=> $url, 'data' => $requestData, 'res' => $this->httpResponse]);
            if ($this->httpResponse['code'] == 200 || $this->httpResponse['code'] == 201) {
                //sync data
                $this->property->update(['ejar_uuid' => $res['Body']['data']['id']]);
                $this->response['message'] = __("Property has been synchronized with ejar");
            } else {
                $this->response['status'] = false;
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $this->response['message'] = __("Something went wrong, please try again later.");
                }
            }
            $this->response['data'] = $res;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }

        return $this->response;
    }

    public function preparePostPropertyData(Property $property): array
    {
        $amenities = $property->amenities->pluck('key')->toArray();
        $propertyAmenitiesList = array_fill_keys(array_values($amenities), 1);
        $propertyAttributes = collect($property->propertyAttributes);
        //appended attributes
        $appendedAttr = [];
        $total_floors = $propertyAttributes->firstWhere("key", "total_floors")?->pivot->value;
        $units_per_floor = $propertyAttributes->firstWhere("key", "units_per_floor")?->pivot->value;
        $unit_count = $propertyAttributes->firstWhere("key", "number_of_units")?->pivot->value;
        $compound_name = $propertyAttributes->firstWhere("key", "compound_name");
        if ($total_floors && $total_floors > 0) {
            $appendedAttr['total_floors'] = $total_floors;
        }
        if ($unit_count && $unit_count > 0) {
            $appendedAttr['unit_count'] = $unit_count;
        }
        if ($units_per_floor && $units_per_floor > 0) {
            $appendedAttr['units_per_floor'] = $units_per_floor;
        }
        if (!empty($compound_name)) {
            $appendedAttr['compound_name'] = $compound_name->pivot->value;
        }

        $owner = $property->owners->first();
        $submittedData = [
            "data" => [
                "ownership_document" => [
                    "attributes" => [
                        "document_number" => $property->documentOwnership->metadata['ownership_reference_no'] ?? null,
                        "issued_date" => $this->getHejriIssueDate(date('Y-m-d', strtotime($property->documentOwnership->metadata['issue_date'])) ?? null),
                        "ownership_document_type" => $property->documentOwnership->metadata['document_type'] ?? null,
                    ],
                ],
                "owners" => [
                    "attributes" => [
                        "owner_id" => $owner->ejar_uuid
                    ],
                ],
                "property" => [
                    "attributes" => [
                        "address" => [
                            "attributes" => [
                                "region_code" => $property->region_id,
                                "city_code" => $property->city_id,
                                "district_code" => $property->district_id,
                                "building_number" => $property->build_number,
                                "additional_number" => "7249", //todo handle additional_num
                                "street_name" => $property->street,
                                "postal_code" => $property->postal_code,
                                "latitude" => $property->lat,
                                "longitude" => $property->lng,
                            ]
                        ],
                        "contract_type" => LeaseTypesEnum::RESIDENTIAL, //todo note this
                        "property_name" => $property->name,
                        "property_number" => $property->number,
                        "availability" => 1, //make it available
                        "property_usage" => $property->usability?->key ?? "residential_families",
                        "property_type" => $property->property_type?->key ?? "building",
                        "established_date" => $property->building_date,
                    ]
                ],
            ]
        ];

        $submittedData['data']['property']['attributes'] = array_merge($submittedData['data']['property']['attributes'], $appendedAttr);
        if (count($propertyAmenitiesList) > 0) {
            $submittedData['data']['property']['attributes']['associated_facilities'] = $propertyAmenitiesList;
        }
        return $submittedData;
    }


    protected function getHejriIssueDate($date): string
    {
        try {
            return Hijri::convertToHijri($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return $date;
        }
    }
}
