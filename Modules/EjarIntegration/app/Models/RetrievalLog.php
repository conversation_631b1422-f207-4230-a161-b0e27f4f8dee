<?php
namespace Modules\EjarIntegration\app\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\Enums\RetrievalStatus;
use Modules\Tenancy\Traits\BelongsToTenancy;

class RetrievalLog extends Model
{
    use BelongsToTenancy;

    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = ['id'];

    protected $casts = [
        'status' => RetrievalStatus::class,
    ];

    public function company(){
        return $this->belongsTo(Company::class);
    }
}
