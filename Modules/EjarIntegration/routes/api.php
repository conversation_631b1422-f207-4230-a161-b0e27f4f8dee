<?php

use Illuminate\Support\Facades\Route;
use Modules\EjarIntegration\app\Http\Controllers\Api\EjarIntegrationController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->prefix('ejar-integration')->group(function () {
    Route::apiResource('ejarintegration', EjarIntegrationController::class)->names('ejarintegration');
});
