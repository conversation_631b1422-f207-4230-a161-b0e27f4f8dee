<?php

namespace Modules\Invoice\app\Filament\Resources;

use Modules\Invoice\app\Filament\Resources\InvoiceResource\Components\FormComponent;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\LineItemsRelationManager;
use Modules\Invoice\app\Models\Invoice;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\InvoicePaymentsRelationManager;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\NajzRequestRelationManager;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Widgets\InvoiceListOverview;
use Illuminate\Support\Facades\DB;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        $list = FormComponent::getList();

        return $table
            ->columns($list['columns'])
            ->filters($list['filters'])
            ->actions($list['actions'])
            ->bulkActions($list['bulkActions'])
            ->searchOnBlur()
            ->persistSearchInSession()
            ->searchPlaceholder(__('Search by ID, Ejar Number, National ID, or Lease Number...'))
            ->searchable(function ($query, $search) {
                if (filled($search)) {
                    $query->where(function ($query) use ($search) {
                        // Search in basic invoice fields
                        $query->where('id', 'like', "%{$search}%")
                            ->orWhere('ejar_number', 'like', "%{$search}%")
                            ->orWhere('for_id', 'like', "%{$search}%")
                            ->orWhere('status', 'like', "%{$search}%")
                            ->orWhere('total', 'like', "%{$search}%")
                            ->orWhere('remaining', 'like', "%{$search}%")
                            // Search in JSON extra field for lease_id
                            ->orWhereRaw('json_unquote(json_extract(`extra`, \'$."lease_id"\')) like ?', ["%{$search}%"])
                            // Search by lease number using subquery
                            ->orWhereExists(function ($subQuery) use ($search) {
                                $subQuery->select(DB::raw(1))
                                    ->from('leases')
                                    ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                                    ->where('leases.lease_number', 'like', "%{$search}%");
                            })
                            // Search by national_id of lease members using subquery
                            ->orWhereExists(function ($subQuery) use ($search) {
                                $subQuery->select(DB::raw(1))
                                    ->from('leases')
                                    ->join('lease_members', 'leases.id', '=', 'lease_members.lease_id')
                                    ->join('accounts', function ($join) {
                                        $join->on('lease_members.member_id', '=', 'accounts.id')
                                             ->where('lease_members.memberable_type', '=', \Modules\Account\app\Models\Account::class);
                                    })
                                    ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                                    ->where('accounts.national_id', 'like', "%{$search}%");
                            });
                    });
                }
                return $query;
            });
    }

    public static function getRelations(): array
    {
        return [
            LineItemsRelationManager::class,
            InvoicePaymentsRelationManager::class,
            NajzRequestRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}/show'),
        ];
    }
    public static function getWidgets(): array
    {
        return [
            InvoiceListOverview::class,
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Invoice');
    }


    public static function getNavigationLabel(): string
    {
        return __("Invoices");
    }

    public static function getBreadcrumb() : string
    {
        return __('Invoice');
    }
    public static function getModelLabel(): string
    {
        return __('Invoice');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Invoice');
    }
}
