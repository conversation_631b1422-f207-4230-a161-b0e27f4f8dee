<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Components;

use App\Models\User;
use Filament\Forms\Components\Hidden;
use Modules\Invoice\app\Models\Invoice;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Account\app\Models\Account;
use Modules\Invoice\Services\InvoiceService;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ViewAction;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Lease\app\Models\Lease;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Illuminate\Support\Facades\DB;
use Modules\Payment\app\Filament\Resources\InvoiceResource\Actions\PayInvoiceAction;
use Filament\Tables\Filters\SelectFilter;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Actions\NajzRequestAction;

class FormComponent
{

    const FROM_TYPE = [
        User::class => 'User',
    ];

    const FOR_TYPE = [
        Account::class => 'Account',
    ];

    public static function getForm(): array
    {
        return [
            // Fieldset for UUID and Status
            Forms\Components\Section::make(__('Invoice Information'))
                ->schema([
                    TextInput::make('ejar_number')
                        ->unique(ignoreRecord: true)
                        ->disabled(fn(Invoice $invoice) => $invoice->exists)
                        ->label(__('UUID'))
                        ->live()
                        ->readOnly()
                        ->required()
                        ->columnSpan(6)
                        ->maxLength(255),
                    Select::make('status')
                        ->default('unpaid')
                        ->label(__('Status'))
                        ->required()
                        ->columnSpan(6)
                        ->options(InvoiceStatusEnum::getInvoiceStatusOptions())
                        ->placeholder(__('Select Status')),
                ])
                ->columns(12)
                ->columnSpanFull(),

            // Fieldset for From Person Information
            Forms\Components\Section::make(__('From Person Information'))
                ->schema([
                    Select::make('from_type')
                        ->label(__('Sender Type'))
                        ->required()
                        ->options(self::FROM_TYPE)
                        ->columnSpan(6)
                        ->searchable()
                        ->placeholder(__('Select Sender Type')),
                    Select::make('from_id')
                        ->label(__('Sender ID'))
                        ->required()
                        ->columnSpan(6)
                        ->options(User::select('name', 'id')->pluck('name', 'id'))
                        ->searchable()
                        ->placeholder(__('Select a Sender User')),
                ])
                ->columns(12)
                ->columnSpanFull(),

            // Fieldset for For Person Information
            Forms\Components\Section::make(__('For Person Information'))
                ->schema([
                    Select::make('for_type')
                        ->label(__('For Type'))
                        ->required()
                        ->columnSpan(6)
                        ->options(self::FOR_TYPE)
                        ->searchable()
                        ->placeholder(__('Select For Type')),
                    Select::make('for_id')
                        ->label(__('For ID'))
                        ->required()
                        ->columnSpan(6)
                        ->options(Account::select(DB::raw("CONCAT(first_name, ' ', second_name, ' ', third_name, ' ', last_name) AS full_name"), 'id')
                        ->pluck('full_name', 'id'))
                        ->searchable()
                        ->placeholder(__('Select a For User')),
                ])
                ->columns(12)
                ->columnSpanFull(),

            // Fieldset for Invoice Dates
            Forms\Components\Section::make(__('Invoice Dates'))
                ->schema([
                    DatePicker::make('release_date')
                        ->nullable()
                        ->native(false)
                        ->columnSpan(6)
                        ->label(__('Release Date'))
                        ->placeholder(__('Select Release Date')),
                    DatePicker::make('due_date')
                        ->nullable()
                        ->native(false)
                        ->columnSpan(6)
                        ->label(__('Due Date'))
                        ->placeholder(__('Select Due Date')),
                ])
                ->columns(12)
                ->columnSpanFull(),

            // Repeater for Invoice Items
            Repeater::make('items')
                ->hiddenLabel()
                ->collapsible()
                ->collapsed(fn($record) => $record)
                ->relationship('items')
                ->label(__('Invoice Item'))
                ->itemLabel(__('Invoice Item'))
                ->maxItems(1)
                ->schema([
                    Forms\Components\Fieldset::make(__('From Person Information'))
                        ->schema([
                            Select::make('item_type')
                                ->label(__('Item Type'))
                                ->required()
                                ->options(InvoiceService::getAvalibaleModelsForInvoiceItemMembers())
                                ->searchable()
                                ->placeholder(__('Select Item Type')),
                            Select::make('item_id')
                                ->label(__('Item'))
                                ->required()
                                ->reactive()
                                ->options(function (callable $get ,Set $set ,$livewire) {
                                    $itemType = $get('item_type');
                                    $type = match($itemType) {
                                        MaintenanceRequest::class => InvoiceItemTypeEnum::MAINTENANCE_REQUEST,
                                        Lease::class => InvoiceItemTypeEnum::OTHER,
                                        default => InvoiceItemTypeEnum::OTHER,
                                    };
                                    $set('type', $type);
                                    $forType = $livewire->data['for_type'];
                                    $forId = $livewire->data['for_id'];
                                    return InvoiceService::fetchModelData($itemType , $forType , $forId);
                                })
                                ->searchable()
                                ->placeholder(__('Select an Item ID'))
                                ->disabled(fn(callable $get) => !$get('item_type'))
                                ->reactive()
                                ->afterStateUpdated(function (Get $get, Set $set,$livewire) {
                                    $itemType = $get('item_type');
                                    $itemId = $get('item_id');
                                    $leaseId = InvoiceService::getItemLeaseId($itemType , $itemId);
                                    $livewire->data['extra'] = $leaseId;
                                    $uuid = rand(0, 9999) .'-'. json_decode($leaseId)->lease_id . '0' . $itemId;
                                    $livewire->data['uuid'] = $uuid;
                                    }),
                                Hidden::make('type')
                                ->live(),
                        ]),
                    TextInput::make('description')
                        ->label(__('Description'))
                        ->columnSpan(6)
                        ->placeholder(__('Enter Description')),
                    TextInput::make('note')
                        ->label(__('Note'))
                        ->columnSpan(6)
                        ->placeholder(__('Enter Note')),
                    TextInput::make('qty')
                        ->live()
                        ->columnSpan(2)
                        ->label(__('Quantity'))
                        ->default(1)
                        ->numeric()
                        ->placeholder(__('Enter Quantity')),
                    TextInput::make('price')
                        ->label(__('Price'))
                        ->columnSpan(3)
                        ->default(0)
                        ->numeric()
                        ->placeholder(__('Enter Price')),
                    TextInput::make('discount')
                        ->label(__('Discount'))
                        ->columnSpan(2)
                        ->default(0)
                        ->numeric()
                        ->placeholder(__('Enter Discount')),
                    TextInput::make('tax')
                        ->label(__('Tax'))
                        ->columnSpan(2)
                        ->default(0)
                        ->reactive()
                        ->numeric()
                        ->placeholder(__('Enter Tax')),
                    TextInput::make('total')
                        ->label(__('Total'))
                        ->columnSpan(3)
                        ->default(0)
                        ->numeric()
                        ->placeholder(__('Enter Total')),
                ])
                ->lazy()
                ->afterStateUpdated(function (Get $get, Set $set) {
                    $items = $get('items');
                    $total = 0;
                    $remaining = 0;
                    $discount = 0;
                    $vat = 0;
                    $collectItems = [];

                    foreach ($items as $invoiceItem) {
                        $getRemaining = (($invoiceItem['price'] + $invoiceItem['tax'] - $invoiceItem['discount']) * $invoiceItem['qty']);
                        $remaining += $getRemaining;
                        $total += $invoiceItem['price'];
                        $invoiceItem['total'] = $getRemaining;
                        $discount += ($invoiceItem['discount'] * $invoiceItem['qty']);
                        $vat += ($invoiceItem['tax'] * $invoiceItem['qty']);
                        $collectItems[] = $invoiceItem;
                    }
                    $paid = $get('paid');
                    $set('remaining', ($remaining - $paid));
                    $set('total', $total);
                    $set('discount', $discount);
                    $set('tax', $vat);
                    $set('items', $collectItems);
                })
                ->columns(12)
                ->columnSpanFull(),

            // Section for Invoice Summary
            Section::make(__('Invoice Summary'))
                ->schema([
                    Hidden::make('extra')
                       ->live(),
                    TextInput::make('tax')
                        ->label(__('Tax'))
                        ->columnSpan(2)
                        ->numeric()
                        ->readonly(),
                    TextInput::make('total')
                        ->columnSpan(2)
                        ->label(__('Total'))
                        ->numeric()
                        ->readonly()
                        ->live()
                        ->afterStateUpdated(function (Get $get, Set $set) {
                            $total = $get('total') ?? 0;
                            $paidAmount = $get('paid') ?? 0;
                            $remainingAmount = max(0, $total - $paidAmount);
                            $set('remaining', $remainingAmount);
                        }),
                    TextInput::make('discount')
                        ->label(__('Discount'))
                        ->columnSpan(2)
                        ->numeric()
                        ->default(0)
                        ->readonly(),
                    TextInput::make('paid')
                        ->label(__('Paid'))
                        ->numeric()
                        ->default(0)
                        ->live()
                        ->columnSpan(3)
                        ->afterStateUpdated(function (Get $get, Set $set) {
                            $total = ($get('total') - $get('discount')) ?? 0;
                            $paidAmount = $get('paid') ?? 0;
                            $remainingAmount = max(0, $total - $paidAmount);
                            $set('remaining', $remainingAmount);

                            if ($paidAmount > 0 && $paidAmount < $total) {
                                $set('status', 'partial_paid');
                            } elseif ($paidAmount >= $total) {
                                $set('status', 'paid');
                            } else {
                                $set('status', 'unpaid');
                            }
                        }),
                    TextInput::make('remaining')
                        ->default(0)
                        ->label(__('Remaining'))
                        ->readonly()
                        ->numeric()
                        ->columnSpan(3),
                    Textarea::make('notes')
                        ->label(__('Notes'))
                        ->columnSpanFull()
                        ->placeholder(__('Enter Notes')),
                ])
                ->columns(12)
                ->columnSpanFull()
                ->collapsible()
                ->collapsed(fn($record) => $record),
        ];
    }


    public static function getList(): array
    {
        return [
            'columns' => [
                TextColumn::make('id')
                    ->label(__('id'))
                    ->sortable()
                    ->searchable(),
                TextColumn::make('ejar_number')
                    ->label(__('Invoice Ejar number'))
                    ->sortable()
                    ->searchable(),
                TextColumn::make('extra.lease_id')
                    ->label(__('Lease ID'))
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(function ($record) {
                        $extra = json_decode($record->extra, true);
                        return $extra['lease_id'] ?? '-';
                    })
                    ->url(function ($record) {
                        $extra = json_decode($record->extra, true);
                        return isset($extra['lease_id'])
                            ? url('admin/leases/'.$extra['lease_id']) // adjust route name
                            : null;
                    })
                    ->openUrlInNewTab(),
                TextColumn::make('property_name')
                ->label(__('Property Name'))
                ->getStateUsing(function ($record) {
                    $extra = json_decode($record->extra, true);
                    $leaseId = $extra['lease_id'] ?? null;

                    if ($leaseId) {
                        $lease = Lease::find($leaseId);
                        if ($lease && $lease->property) {
                            return $lease->property->name ?? '-';
                        }
                    }

                    return '-';
                }),

                TextColumn::make('lease_number')
                    ->label(__('lease Ejar Number'))
                    ->sortable(),

                TextColumn::make('for_id')
                    ->label(__('For'))
                    ->sortable()
                    ->searchable()
                    ->getStateUsing(function ($record) {
                        // Determine the model class based on 'from_type'
                        $modelClass = $record->for_type;

                        if (class_exists($modelClass)) {
                            // Attempt to find the related model by 'from_id'
                            $relatedModel = $modelClass::find($record->for_id);

                            if ($relatedModel) {
                                // Return the 'name' or any identifying attribute of the model
                                return $relatedModel->name ?? $relatedModel->first_name ?? 'N/A';
                            }
                        }

                        return 'N/A'; // If model not found or 'from_id' is invalid
                    }),

                    TextColumn::make('status')
                    ->label(__('Status'))
                    ->sortable()
                    ->badge()
                    ->searchable()
                    ->getStateUsing(fn ($record) => \Modules\Invoice\Enums\InvoiceStatusEnum::getLabel($record->status)),

                TextColumn::make(name: 'total')
                    ->label(__('Total'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'remaining')
                    ->label(__('Remaining Amount'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ],
            'filters' => [
                // Add any filters here if needed
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(InvoiceStatusEnum::getInvoiceStatusOptions())
                    ->native(false)
                    ->searchable(),
                SelectFilter::make('for_id')
                    ->label('Recipient')
                    ->placeholder(__('Select Recipient'))
                    ->getOptionLabelUsing(fn ($value): ?string => Account::find($value)?->name)
                    ->native(false)
                    ->preload(false)
                    ->searchable(['name'])
                    ->getSearchResultsUsing(function (string $search) {
                        return Account::where('name', 'like', "%{$search}%")
                            ->select('id', 'name')
                            ->limit(50)
                            ->get()
                            ->pluck('name', 'id')
                            ->toArray();
                    }),
                SelectFilter::make('extra')
                    ->label(__('Lease'))
                    ->placeholder(__('Select Lease'))
                    ->options(Lease::pluck('id','id'))
                    ->query(function($query, $state) {
                        if ($state && !is_null($state['value'])) {
                            $state = (int) $state['value'];
                            return $query->whereRaw('JSON_EXTRACT(extra, "$.lease_id") = ?', [$state]);
                        }

                        return $query;
                    })
                    ->native(false)
                    ->searchable(),

                    SelectFilter::make('property_name')
                    ->label(__('Property Name'))
                    ->placeholder(__('Select Property'))
                    ->options(function() {
                        // Get all properties associated with leases
                        $properties = [];
                        $leases = Lease::with('property')->get();
                        foreach ($leases as $lease) {
                            if ($lease->property) {
                                $properties[$lease->property->id] = $lease->property->name;
                            }
                        }
                        return $properties;
                    })
                    ->query(function($query, $state) {
                        if ($state && !is_null($state['value'])) {
                            $propertyId = (int) $state['value'];

                            $leaseIds = Lease::where('property_id', $propertyId)
                                ->pluck('id')
                                ->toArray();

                            if (!empty($leaseIds)) {
                                $conditions = [];
                                $bindings = [];

                                foreach ($leaseIds as $leaseId) {
                                    $conditions[] = 'JSON_EXTRACT(extra, "$.lease_id") = ?';
                                    $bindings[] = $leaseId;
                                }

                                return $query->whereRaw('(' . implode(' OR ', $conditions) . ')', $bindings);
                            }
                        }

                        return $query;
                    })
                    ->native(false)
                    ->searchable()

            ],
            'actions' => [
                ViewAction::make(), // Enable the view action
                PayInvoiceAction::make(), // Add this line
                NajzRequestAction::make(),
            ],
            'bulkActions' => [
                // Add any bulk actions here if needed
            ],
        ];
    }
}
