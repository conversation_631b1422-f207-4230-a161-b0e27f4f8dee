<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;

use Modules\Invoice\app\Filament\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Widgets\InvoiceListOverview;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Table;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Invoice')),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            InvoiceListOverview::class,
        ];
    }

    public function table(Table $table): Table
    {
        return parent::table($table)
            ->modifyQueryUsing(function (Builder $query) {
                // Get the search term from the table state
                $search = $this->getTableSearch();

                if (filled($search)) {
                    $query->where(function ($query) use ($search) {
                        // Search in basic invoice fields
                        $query->where('id', 'like', "%{$search}%")
                            ->orWhere('ejar_number', 'like', "%{$search}%")
                            ->orWhere('for_id', 'like', "%{$search}%")
                            ->orWhere('status', 'like', "%{$search}%")
                            ->orWhere('total', 'like', "%{$search}%")
                            ->orWhere('remaining', 'like', "%{$search}%")
                            // Search in JSON extra field for lease_id
                            ->orWhereRaw('json_unquote(json_extract(`extra`, \'$."lease_id"\')) like ?', ["%{$search}%"])
                            // Search by lease number using subquery
                            ->orWhereExists(function ($subQuery) use ($search) {
                                $subQuery->select(DB::raw(1))
                                    ->from('leases')
                                    ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                                    ->where('leases.lease_number', 'like', "%{$search}%");
                            })
                            // Search by national_id of lease members using subquery
                            ->orWhereExists(function ($subQuery) use ($search) {
                                $subQuery->select(DB::raw(1))
                                    ->from('leases')
                                    ->join('lease_members', 'leases.id', '=', 'lease_members.lease_id')
                                    ->join('accounts', function ($join) {
                                        $join->on('lease_members.member_id', '=', 'accounts.id')
                                             ->where('lease_members.memberable_type', '=', \Modules\Account\app\Models\Account::class);
                                    })
                                    ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                                    ->where('accounts.national_id', 'like', "%{$search}%");
                            });
                    });
                }
            });
    }
}
