<?php

namespace Modules\MaintenanceRequest\Enums;

enum maintenanceRequestStatusEnum: string
{
    const PENDING = 'pending';
    const OPEN = 'open';
    const TERMINATED = 'terminated';
    const CLOSED = 'closed';

    public static function getMaintenanceRequestStatusOptions(): array
    {
        return [
            self::PENDING => __('Pending'),
            self::OPEN => __('Open'),
            self::CLOSED => __('Closed'),
            self::TERMINATED => __('Terminated'),
        ];
    }
    public static function getMaintenanceRequestStatusColors(): array
    {
        return [
            self::PENDING => '#28a745', // Green for Low
            self::OPEN => '#ffc107', // Yellow for Medium
            self::CLOSED => '#6c757d', //Grey for Close
            self::TERMINATED => '#dc3545', // Red for High
        ];
    }

    public static function getMaintenanceRequestStatusValues(): array
    {
        return [
            self::PENDING,
            self::OPEN,
            self::CLOSED,
            self::TERMINATED,
        ];
    }

    public static function labels(): array
    {
        return [
            self::PENDING => __('Pending'),
            self::OPEN => __('Open'),
            self::CLOSED => __('Closed'),
            self::TERMINATED => __('Terminated'),
        ];
    }

    public static function trans(string $status): string
    {
        return match($status) {
            self::PENDING => __('Pending'),
            self::OPEN => __('Open'),
            self::CLOSED => __('Closed'),
            self::TERMINATED => __('Terminated'),
            default => $status,
        };
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
}
