<?php

namespace Modules\Notification\app\Http\Requests;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class UserNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title ,
            'description' => $this->description ,
            'key' => $this->template->key ?? null,
            'type' => $this->type,
            'url' => $this->url,
            'is_read' => $this->is_read,
            'image' => $this->icon,
            'date' => Carbon::parse($this->created_at)->diffForHumans(),
            'data' => $this->data,
            "created_at" => $this->created_at
        ];
    }
}
