<?php

namespace Modules\Notification\app\Models;

//use Eloquent as Model;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\Permission\Models\Role;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class NotificationsTemplate extends Model implements HasMedia
{
    use HasTranslations;
    use InteractsWithMedia;

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    public $translatable = ['title', 'body'];

    public $fillable = [
        'id',
        'name',
        'key',
        'body',
        'title',
        'url',
        'icon',
        'type',
        'providers',
        'action'
    ];

    protected $casts = [
        "providers" => "array",
        "title" => "array",
        "body" => "array"
    ];


    public function roles()
    {
        return $this->belongsToMany(Role::class, 'template_has_roles', 'template_id', 'role_id');
    }
}
