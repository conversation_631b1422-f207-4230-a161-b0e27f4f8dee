<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->string('ejar_uuid')->nullable();
            $table->string('organization_type')->nullable();
            $table->string('registration_number')->nullable();
            $table->date('registration_date')->nullable();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn('ejar_uuid');
            $table->dropColumn('organization_type');
            $table->dropColumn('registration_number');
            $table->dropColumn('registration_date');
        });
    }
};
