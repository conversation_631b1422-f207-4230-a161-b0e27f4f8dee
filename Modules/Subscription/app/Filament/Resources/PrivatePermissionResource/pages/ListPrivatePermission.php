<?php

namespace Modules\Subscription\app\Filament\Resources\PrivatePermissionResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;

class ListPrivatePermission extends ListRecords
{
    protected static string $resource = PrivatePermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
