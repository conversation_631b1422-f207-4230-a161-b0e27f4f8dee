<?php

namespace Modules\Subscription\app\Http\Middleware;

use App\Enums\RoleEnum;
use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Modules\Subscription\app\models\PrivatePermission;
use Modules\Subscription\Helpers\MiddlewareHelper;

class Subscription
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        if (is_null(auth()->user()->company))
            return $next($request);


        $route = MiddlewareHelper::getShieldPermissionName($request->route()->getName());
        return MiddlewareHelper::checkPermission($request,$next,$route);

    }



}

/*

 * create seeder for all api permissions
 * cron job to delete un used subscriptions
 * private permissions
 * */
