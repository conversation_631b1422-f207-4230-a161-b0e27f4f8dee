<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attributes', function (Blueprint $table) {
                $table->id();
                $table->json('name');
                $table->string('data_type')->default('string');
                $table->boolean('is_required')->default(0);
                $table->boolean('is_active')->default(1);
                $table->softDeletes();

                $table->timestamps();

        });

        Schema::create('attribute_type', function (Blueprint $table) {
                $table->id();
                $table->foreignId('property_type_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
                $table->foreignId('attribute_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
                $table->softDeletes();
                $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attributes');
        Schema::dropIfExists('attribute_type');
    }
};
