<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vals', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('morphable');
            $table->string('value');
            $table->date('start_date')->default(now());
            $table->date('end_date')->default(now()->addYear());
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vals');
    }
};
