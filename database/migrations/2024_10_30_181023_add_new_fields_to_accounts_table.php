<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Add your new fields here
            $table->string('name')->nullable()->after('id');

            // Example: If you want to add multiple fields
            // $table->string('another_field')->nullable()->after('new_field_name');
            // $table->integer('some_number')->nullable()->after('another_field');
        });
    }

    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Drop the fields in reverse order
            $table->dropColumn('name');

            // For multiple fields
            // $table->dropColumn(['new_field_name', 'another_field', 'some_number']);
        });
    }
};
