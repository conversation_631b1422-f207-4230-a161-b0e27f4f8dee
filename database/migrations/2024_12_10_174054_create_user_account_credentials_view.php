<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Check and add company_id column if it does not exist
        if (!Schema::hasColumn('users', 'company_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->unsignedBigInteger('company_id')->nullable()->after('id'); // adjust position as needed
            });
        }

        // Step 2: Create the view using company_id
        DB::statement("
            CREATE OR REPLACE VIEW user_account_credentials AS
            SELECT
                u.id as user_id,
                a.id as account_id,
                COALESCE(a.national_id, up.national_id) as national_id,
                COALESCE(a.password, u.password) as password,
                CASE
                    WHEN a.id IS NULL THEN u.company_id
                    ELSE NULL
                END as company_id
            FROM users u
            RIGHT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN accounts a ON up.national_id = a.national_id AND a.deleted_at IS NULL

            UNION

            SELECT
                NULL as user_id,
                a.id as account_id,
                a.national_id,
                a.password,
                NULL as company_id
            FROM accounts a
            LEFT JOIN user_profiles up ON up.national_id = a.national_id
            WHERE up.national_id IS NULL
            AND a.deleted_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the view
        DB::statement('DROP VIEW IF EXISTS user_account_credentials');

        // Optionally drop the column only if you want to reverse the column addition
        if (Schema::hasColumn('users', 'company_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('company_id');
            });
        }
    }
};
