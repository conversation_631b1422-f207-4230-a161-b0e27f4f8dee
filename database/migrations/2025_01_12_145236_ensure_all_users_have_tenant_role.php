<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Modules\Account\Enums\AccountRolesEnum;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(function () {
            // Find users with 'No Role'
            $usersWithNoRoles = DB::select("
                SELECT uac.user_id, uac.account_id
                FROM user_account_credentials uac
                LEFT JOIN account_roles ar ON uac.account_id = ar.account_id
                GROUP BY uac.user_id, uac.account_id
                HAVING COALESCE(GROUP_CONCAT(DISTINCT ar.role ORDER BY ar.role SEPARATOR ', '), 'No Role') = 'No Role'
            ");

            // Prepare batch insert data
            $now = now();
            $rolesData = array_reduce($usersWithNoRoles, function ($array, $user) use ($now) {
                if ($user->account_id) {
                    $array[] = [
                        'account_id' => $user->account_id,
                        'role' => AccountRolesEnum::TENANT,
                        'is_default' => 1,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }
                return $array;
            }, []);

            // Batch insert if there are roles to add
            if (!empty($rolesData)) {
                DB::table('account_roles')->insert($rolesData);
            }
        });
    }

    public function down(): void
    {
        DB::table('account_roles')
            ->where([
                'role' => AccountRolesEnum::TENANT,
                'is_default' => 1
            ])
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('user_account_credentials as uac')
                    ->whereRaw('account_roles.account_id = uac.account_id');
            })
            ->delete();
    }
};
