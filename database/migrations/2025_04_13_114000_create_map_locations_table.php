<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // regions migration
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->json('name')->nullable();
            $table->string('longitude')->nullable();
            $table->string('latitude')->nullable();
        });

        // cities migration
        Schema::create('cities', function (Blueprint $table) {
            $table->id();
            $table->json('name')->nullable();
            $table->string('longitude')->nullable();
            $table->string('latitude')->nullable();
            $table->foreignId('region_id')->nullable()->constrained();
        });

        // districts migration
        Schema::create('districts', function (Blueprint $table) {
            $table->id();
            $table->json('name')->nullable();
            $table->string('longitude')->nullable();
            $table->string('latitude')->nullable();
            $table->boolean('is_active')->nullable()->default(true);
            $table->foreignId('city_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('districts');
        Schema::dropIfExists('cities');
        Schema::dropIfExists('regions');
    }
};
