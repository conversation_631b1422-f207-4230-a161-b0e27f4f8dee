<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->string('id_type')->default('national_id')->after('phone');
            $table->string('country_of_issue')->nullable()->after('id_type');
            $table->string('custom_id_type')->nullable()->after('country_of_issue');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn([
                'id_type',
                'country_of_issue',
                'custom_id_type',
            ]);
        });
    }
};
