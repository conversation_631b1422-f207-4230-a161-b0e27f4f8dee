<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing NULL values to 'SA'
        DB::table('user_profiles')
            ->whereNull('country_of_issue')
            ->update(['country_of_issue' => 'SA']);

        // Then modify the column to set the default value
        Schema::table('user_profiles', function (Blueprint $table) {
            $table->string('country_of_issue')->nullable()->default('SA')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            $table->string('country_of_issue')->nullable()->default(null)->change();
        });
    }
};
