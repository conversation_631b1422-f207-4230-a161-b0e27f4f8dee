<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attributes', function (Blueprint $table) {
            $table->boolean('deletable')->default(1)->after('is_active');
        });

        DB::table('attributes')->update(['is_required' => 0]);
        $specialKeys = ['number_of_units', 'total_floors', 'floor_number', 'units_per_floor'];
        DB::table('attributes')
            ->whereIn('key', $specialKeys)
            ->update([
                'is_required' => 1,
                'deletable' => 0
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attributes', function (Blueprint $table) {
            //
        });
    }
};
