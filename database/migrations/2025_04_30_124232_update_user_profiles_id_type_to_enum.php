<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Enums\IdTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            // Change the column to enum
            $table->enum('id_type', [
                'national_id',
                'residency_permit',
                'passport',
                'gcc_id',
                'other'
            ])->change();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            $table->string('id_type', 50)->change();
        });
    }
};
