<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->foreignId('bank_account_id')->nullable()->constrained()->references('id')->on('bank_accounts');
        });

        // Migrate data from lease_members to leases
        $tenants = DB::table('lease_members')
            ->whereNotNull('bank_account_id')
            ->select('lease_id', 'bank_account_id')
            ->get();

        foreach ($tenants as $tenant) {
            DB::table('leases')
                ->where('id', $tenant->lease_id)
                ->update(['bank_account_id' => $tenant->bank_account_id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, add the bank_account_id column if it doesn't exist in lease_members table
        if (!Schema::hasColumn('lease_members', 'bank_account_id')) {
            Schema::table('lease_members', function (Blueprint $table) {
                $table->foreignId('bank_account_id')->nullable();
            });
            
            Schema::table('lease_members', function (Blueprint $table) {
                $table->foreign('bank_account_id')->references('id')->on('bank_accounts');
            });
        }
        
        // Get the bank_account_id from leases and add it back to the tenant in lease_members before dropping
        $leases = DB::table('leases')
            ->whereNotNull('bank_account_id')
            ->select('id', 'bank_account_id')
            ->get();

        foreach ($leases as $lease) {
            // Find the tenant record for this lease and update it
            $tenant = DB::table('lease_members')
                ->where('lease_id', $lease->id)
                ->where('member_role', 'tenant')
                ->first();

            if ($tenant) {
                DB::table('lease_members')
                    ->where('id', $tenant->id)
                    ->update(['bank_account_id' => $lease->bank_account_id]);
            }
        }

        Schema::table('leases', function (Blueprint $table) {
            $table->dropForeign(['bank_account_id']);
            $table->dropColumn('bank_account_id');
        });
    }
};
