<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            // First drop the foreign key constraint
            $table->dropForeign(['bank_account_id']);
            // Then drop the column
            $table->dropColumn('bank_account_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->foreignId('bank_account_id')->nullable();
        });

        Schema::table('lease_members', function (Blueprint $table) {
            $table->foreign('bank_account_id')->references('id')->on('bank_accounts');
        });
    }
};
