<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nafath_logs', function (Blueprint $table) {
            $table->id();
            $table->string('token', 1000); // Large enough to store long tokens
            $table->string('trans_id')->nullable();
            $table->string('request_id')->nullable();
            $table->string('national_id')->nullable();
            $table->string('status')->nullable();
            $table->json('decoded_payload')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nafath_logs');
    }
};
