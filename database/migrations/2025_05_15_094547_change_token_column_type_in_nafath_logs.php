<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nafath_logs', function (Blueprint $table) {
            $table->longText('token')->nullable()->change(); // Just change the type
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nafath_logs', function (Blueprint $table) {
            $table->string('token', 1000)->nullable()->change(); // Revert to previous type
        });
    }
};
