<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\PropertyType;

class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $allAttributes = [
            ['name' => ['en' => 'Number of rooms', 'ar' => 'عدد الغرف'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'rooms'],
            ['name' => ['en' => 'Number of bathrooms', 'ar' => 'عدد الحمامات'], 'icon_url' => 'https://www.svgrepo.com/show/142468/double-sink.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_bathrooms'],
            ['name' => ['en' => 'Total area', 'ar' => 'المساحة الكلية'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'area'],
            ['name' => ['en' => 'Number of floors', 'ar' => 'عدد الطوابق'], 'icon_url' => 'https://www.svgrepo.com/show/368271/floors-solid.svg', 'is_active' => true, 'is_required' => false, 'key' => 'total_floors'],
            ['name' => ['en' => 'Year built', 'ar' => 'سنة البناء'], 'icon_url' => 'https://www.svgrepo.com/show/457647/date-range.svg', 'is_active' => true, 'is_required' => false, 'key' => 'year_built'],
            ['name' => ['en' => 'Floor number', 'ar' => 'رقم الطابق'], 'icon_url' => 'https://www.svgrepo.com/show/142468/double-sink.svg', 'is_active' => true, 'is_required' => true, 'key' => 'floor_number'],
            ['name' => ['en' => 'Balcony', 'ar' => 'شرفة'], 'icon_url' => 'https://www.svgrepo.com/show/455883/balcony.svg', 'is_active' => true, 'is_required' => false, 'key' => 'balcony'],
            ['name' => ['en' => 'Garden area', 'ar' => 'مساحة الحديقة'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => false, 'key' => 'garden_area'],
            ['name' => ['en' => 'Number of apartments', 'ar' => 'عدد الشقق'], 'icon_url' => 'https://www.svgrepo.com/show/368271/floors-solid.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_apartments'],
            ['name' => ['en' => 'Storefront width', 'ar' => 'عرض الواجهة'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'storefront_width'],
            ['name' => ['en' => 'Land use', 'ar' => 'استخدام الأرض'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'land_use'],
            ['name' => ['en' => 'Height', 'ar' => 'الارتفاع'], 'icon_url' => 'https://www.svgrepo.com/show/378630/height.svg', 'is_active' => true, 'is_required' => true, 'key' => 'height'],
            ['name' => ['en' => 'Production capacity', 'ar' => 'القدرة الإنتاجية'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => false, 'key' => 'production_capacity'],
            ['name' => ['en' => 'Star rating', 'ar' => 'تصنيف النجوم'], 'icon_url' => 'https://www.svgrepo.com/show/422133/star-rating-check-mark.svg', 'is_active' => true, 'is_required' => true, 'key' => 'star_rating'],
            ['name' => ['en' => 'Number of units', 'ar' => 'عدد الوحدات'], 'icon_url' => 'https://www.svgrepo.com/show/368271/floors-solid.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_units'],
            ['name' => ['en' => 'Amenities', 'ar' => 'المرافق'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'amenities'],
            ['name' => ['en' => 'Type of crops', 'ar' => 'نوع المحاصيل'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => false, 'key' => 'type_of_crops'],
            ['name' => ['en' => 'Capacity', 'ar' => 'السعة'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'capacity'],
            ['name' => ['en' => 'Type', 'ar' => 'النوع'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'type'],
            ['name' => ['en' => 'Seating capacity', 'ar' => 'سعة الجلوس'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'seating_capacity'],
            ['name' => ['en' => 'Kitchen area', 'ar' => 'مساحة المطبخ'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'kitchen_area'],
            ['name' => ['en' => 'Outdoor seating', 'ar' => 'جلسات خارجية'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => false, 'key' => 'outdoor_seating'],
            ['name' => ['en' => 'Frontage', 'ar' => 'الواجهة'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'frontage'],
            ['name' => ['en' => 'Power supply', 'ar' => 'إمدادات الطاقة'], 'icon_url' => 'https://www.svgrepo.com/show/342670/input-power.svg', 'is_active' => true, 'is_required' => true, 'key' => 'power_supply'],
            ['name' => ['en' => 'Zoning', 'ar' => 'التقسيم'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'zoning'],
            ['name' => ['en' => 'Utilities available', 'ar' => 'المرافق المتاحة'], 'icon_url' => 'https://www.svgrepo.com/show/447880/utilities.svg', 'is_active' => true, 'is_required' => true, 'key' => 'utilities_available'],
            ['name' => ['en' => 'Terrace area', 'ar' => 'مساحة التراس'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => false, 'key' => 'terrace_area'],
            ['name' => ['en' => 'Land area', 'ar' => 'مساحة الأرض'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => true, 'key' => 'land_area'],
            ['name' => ['en' => 'Coastline length', 'ar' => 'طول الساحل'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => false, 'key' => 'coastline_length'],
            ['name' => ['en' => 'Grazing land area', 'ar' => 'مساحة أرض الرعي'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => false, 'key' => 'grazing_land_area'],
            ['name' => ['en' => 'Number of cabins', 'ar' => 'عدد الكبائن'], 'icon_url' => 'https://www.svgrepo.com/show/12299/cabin.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_cabins'],
            ['name' => ['en' => 'Number of shops', 'ar' => 'عدد المحلات'], 'icon_url' => 'https://www.svgrepo.com/show/391079/shop.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_shops'],
            ['name' => ['en' => 'Parking capacity', 'ar' => 'سعة مواقف السيارات'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'parking_capacity'],
            ['name' => ['en' => 'Number of screens', 'ar' => 'عدد الشاشات'], 'icon_url' => 'https://www.svgrepo.com/show/478106/display-free.svg', 'is_active' => true, 'is_required' => true, 'key' => 'number_of_screens'],
            ['name' => ['en' => 'Power capacity', 'ar' => 'القدرة الكهربائية'], 'icon_url' => 'https://www.svgrepo.com/show/7703/room.svg', 'is_active' => true, 'is_required' => true, 'key' => 'power_capacity'],
            ['name' => ['en' => 'Tower height', 'ar' => 'ارتفاع البرج'], 'icon_url' => 'https://www.svgrepo.com/show/37812630/height.svg', 'is_active' => true, 'is_required' => true, 'key' => 'tower_height'],
            ['name' => ['en' => 'Length', 'ar' => 'الارتفاع'], 'icon_url' => 'https://www.svgrepo.com/show/161124/measuring-ruler.svg', 'is_active' => true, 'is_required' => true, 'key' => 'length'],
            ['name' => ['en' => 'Furnished', 'ar' => 'مفروشة'], 'icon_url' => 'https://www.svgrepo.com/show/33039/appartments.svg', 'is_active' => true, 'is_required' => true, 'key' => 'furnished'],
            ['name' => ['en' => 'Furnish Type', 'ar' => 'نوع الفرش'], 'icon_url' => 'https://www.svgrepo.com/show/282630/real-estate-house-key.svg', 'is_active' => true, 'is_required' => true, 'key' => 'furnish_type'],
            ['name' => ['en' => 'Direction', 'ar' => 'الاتجاه'], 'icon_url' => 'https://www.svgrepo.com/show/489723/direction.svg', 'is_active' => true, 'is_required' => true, 'key' => 'direction'],
            ['name' => ['en' => 'Include Mezzanine', 'ar' => 'تتضمن التمويل المتوسط'], 'icon_url' => 'https://www.svgrepo.com/show/512503/money-round-1184.svg', 'is_active' => true, 'is_required' => true, 'key' => 'include_mezzanine'],
            ['name' => ['en' => 'Finished', 'ar' => 'تم التشطيب'], 'icon_url' => 'https://www.svgrepo.com/show/511868/done-1476.svg', 'is_active' => true, 'is_required' => true, 'key' => 'finished'],
            ['name' => ['en' => 'Saudi NCW Request', 'ar' => 'طلب المجلس القومي للمرأة'], 'icon_url' => 'https://www.svgrepo.com/show/16974/saudi-riyal.svg', 'is_active' => true, 'is_required' => false, 'key' => 'saudi_ncw_request'],
            ['name' => ['en' => 'Saudi NCW Response', 'ar' => 'استجابة المجلس القومي للمرأة'], 'icon_url' => 'https://www.svgrepo.com/show/16974/saudi-riyal.svg', 'is_active' => true, 'is_required' => false, 'key' => 'saudi_ncw_response'],
            ['name' => ['en' => 'Compound Name', 'ar' => 'اسم الكومباوند'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => false, 'key' => 'compound_name'],
            ['name' => ['en' => 'Number Of Units Per Floor', 'ar' => 'عدد الوحدات لكل طابق'], 'icon_url' => 'https://www.svgrepo.com/show/521078/area-1.svg', 'is_active' => true, 'is_required' => false, 'key' => 'units_per_floor'],
        ];

        // Create all attributes
        $attributeMap = [];
        foreach ($allAttributes as $attributeData) {
            $attribute = Attribute::where('name->en', $attributeData['name']['en'])->first();

            if (!$attribute) {
                // Create new attribute
                $attribute = Attribute::create(
                    [
                        'name' => $attributeData['name'],
                        'is_active' => $attributeData['is_active'],
                        'is_required' => $attributeData['is_required'],
                    ]
                );
                // Add media for new attribute
                $this->addAttributeMedia($attribute, $attributeData['icon_url'], 'attribute');

            }

            if (is_null($attribute->key)) {
                $attribute->key = $attributeData['key'];
                $attribute->save();
            }
            $attributeMap[$attributeData['name']['en']] = $attribute->id;

        }

        // Define property types and their attributes
        $propertyTypeAttributes = [
            'Building' => ['Number of floors', 'Total area', 'Furnish Type','Number of apartments', 'Year built', 'Number of units', 'Compound Name', 'Number Of Units Per Floor'],
            'Villa' => ['Number of rooms', 'Number of bathrooms', 'Total area', 'Furnish Type','Number of floors', 'Garden area'],
            'Plazza' => ['Total area', 'Number of units', 'Furnish Type','Number of shops', 'Number of floors', 'Parking capacity'],
            'Land' => ['Total area', 'Land use', 'Zoning', 'Utilities available'],
            'Shopping mall' => ['Total area', 'Furnish Type', 'Floor number', 'Furnish Type','Number of shops', 'Number of units', 'Number of floors', 'Parking capacity'],
            'Tower' => ['Number of floors', 'Total area', 'Floor number', 'Furnish Type', 'Number of units', 'Height'],
            'Factory' => ['Total area', 'Furnish Type','Floor number', 'Production capacity', 'Number of units', 'Power supply'],
            'Rest House' => ['Number of rooms', 'Floor number', 'Number of bathrooms', 'Total area', 'Furnish Type','Garden area', 'Number of units', 'Compound Name', 'Number Of Units Per Floor'],
            'Farm' => ['Total area', 'Furnish Type','Land use', 'Type of crops', 'Grazing land area'],
            'Hotel' => ['Number of rooms', 'Floor number','Star rating', 'Total area', 'Number of floors', 'Amenities', 'Number of units', 'Compound Name', 'Number Of Units Per Floor'],
            'Car Parking' => ['Total area', 'Parking capacity', 'Floor number', 'Number of floors'],
            'Floor' => ['Total area', 'Furnish Type','Floor number', 'Number of apartments'],
            'Apartment' => ['Number of rooms', 'Number of bathrooms', 'Total area', 'Furnish Type','Floor number', 'Balcony', 'Compound Name'],
            'Duplex' => ['Number of rooms', 'Number of bathrooms', 'Total area', 'Furnish Type','Floor number', 'Number of floors', 'Number of units', 'Compound Name', 'Number Of Units Per Floor'],
            'Studio' => ['Total area', 'Furnish Type','Floor number'],
            'Secondary apartment' => ['Number of rooms', 'Floor number', 'Number of bathrooms', 'Total area', 'Furnish Type','Compound Name', 'Number Of Units Per Floor'],
            'Apartment & Extension' => ['Number of rooms', 'Floor number', 'Number of bathrooms', 'Total area', 'Furnish Type','Terrace area', 'Compound Name', 'Number Of Units Per Floor'],
            'Floor & Extension' => ['Total area', 'Furnish Type','Floor number', 'Number of rooms', 'Terrace area', 'Compound Name', 'Number Of Units Per Floor'],
            'Penthouse Villa' => ['Number of rooms', 'Floor number', 'Number of bathrooms', 'Total area', 'Furnish Type','Terrace area', 'Number of units', 'Compound Name', 'Number Of Units Per Floor'],
            'Driver\'s Room' => ['Total area'],
            'Kiosk' => ['Total area', 'Furnish Type','Storefront width'],
            'Shop' => ['Total area', 'Furnish Type','Storefront width'],
            'Workshop' => ['Total area', 'Furnish Type','Floor number', 'Power supply'],
            'Fenced land lot' => ['Total area', 'Land use'],
            'Station' => ['Total area', 'Furnish Type','Type'],
            'Office space' => ['Total area', 'Furnish Type','Number of rooms','Floor number', 'Compound Name', 'Number of units', 'Number Of Units Per Floor'],
            'Warehouse' => ['Total area', 'Floor number', 'Furnish Type','Height'],
            'Trade Exhibition' => ['Total area', 'Furnish Type', 'Number of units','Number of units'],
            'ATM' => ['Total area'],
            'Cinema' => ['Total area', 'Number of screens', 'Floor number', 'Seating capacity'],
            'Power Substation' => ['Total area', 'Floor number', 'Power capacity'],
            'Telecom Tower' => ['Total area', 'Floor number', 'Number of units', 'Tower height'],
            'Plaza' => ['Total area', 'Floor number','Number of shops', 'Number of units', 'Parking capacity'],
            'Other' => ['Total area', 'Furnish Type', 'Floor number', 'Type', 'Number of units'],
        ];

        // Assign attributes to property types
        foreach ($propertyTypeAttributes as $propertyTypeName => $attributeNames) {
            $propertyType = PropertyType::where('name->en', $propertyTypeName)->first();

            if ($propertyType) {
                $attributeIds = array_map(function ($name) use ($attributeMap) {
                    return $attributeMap[$name];
                }, $attributeNames);

                $propertyType->attributes()->sync($attributeIds);
            }
        }
    }

// Helper function to add media
    private function addAttributeMedia(Attribute $modelInstance, string $iconPath, string $collectionName)
    {
        try {
            if (isset($iconPath) && !empty($iconPath)) {
                $modelInstance->addMediaFromUrl($iconPath)
                    ->preservingOriginal()
                    ->toMediaCollection($collectionName);
            }
        } catch (\Exception $ex) {
            // Handle or log the exception as needed
        }
    }


}
