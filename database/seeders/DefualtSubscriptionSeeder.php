<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\Subscription\app\models\Feature;
use Modules\Subscription\app\models\Plan;
use Modules\Subscription\app\models\PlanFeature;
use Modules\Subscription\app\models\PrivatePermission;
use Spatie\Permission\Models\Permission;

class DefualtSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //

        $permissions = Permission::whereIn('name', ['create_user', 'create_property', 'create_lease'])->get();
        // Insert private permissions
        $privatePermissions = [
            [
                'permission_id' => $permissions->where('name', 'create_user')->first()->id,
                'name' => null,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'permission_id' => $permissions->where('name', 'create_property')->first()->id,
                'name' => null,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'permission_id' => $permissions->where('name', 'create_lease')->first()->id,
                'name' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        if (PrivatePermission::select('id')->get()->count() == 0) {
            PrivatePermission::insert($privatePermissions);
        }

        // Insert features
        $features = [
            [
                'name' => [
                    'ar' => 'انشاء عقار',
                    'en' => 'create property'
                ],
                'description' => null,
                'private_permission_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => [
                    'ar' => 'انشاء عقد',
                    'en' => 'create lease'
                ],
                'description' => null,
                'private_permission_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => [
                    'ar' => 'اضافة مستخدم',
                    'en' => 'create user'
                ],
                'description' => null,
                'private_permission_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        if (Feature::select('id')->count() == 0)
            foreach ($features as $feature)
            Feature::create($feature);


        if (Plan::select('id')->get()->count() == 0)
        // Insert plan
        Plan::create([
            'name' => [
                'ar' => 'الخطة الافتراضية',
                'en' => 'Default Plane'
            ],
            'description' => [
                'ar' => 'هذه الخطة الافتراضيه لاخذ جوله فى منصتنا',
                'en' => 'a trial plane to check our platform'
            ],
            'order' => 1,
            'price' => 0.00,
            'discount_price' => 0.00,
            'interval' => 'month',
            'interval_count' => 1,
            'is_default' => 1,
//            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Insert plan features
        $planFeatures = [
            [
                'plan_id' => 1,
                'feature_id' => 1,
                'value' => 2,
                'description' => null,
//                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'plan_id' => 1,
                'feature_id' => 2,
                'value' => 15,
                'description' => null,
//                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'plan_id' => 1,
                'feature_id' => 3,
                'value' => 2,
                'description' => null,
//                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        if (PlanFeature::select('id')->get()->count() == 0)
            PlanFeature::insert($planFeatures);

    }
}
