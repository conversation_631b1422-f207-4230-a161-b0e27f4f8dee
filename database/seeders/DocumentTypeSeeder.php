<?php

namespace Database\Seeders;

use App\Models\DocumentType;
use App\Enums\DocumentTypeEnum;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DocumentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $documentTypes = [
            [
                'name' => [
                    'en' => 'Ownership',
                    'ar' => 'ملكية',
                ],
                'key' => DocumentTypeEnum::Ownership->value,
            ],
            [
                'name' => [
                    'en' => 'Representative',
                    'ar' => 'الممثل',
                ],
                'key' => DocumentTypeEnum::Representative->value,
            ]
        ];

        foreach ($documentTypes as $documentType) {
            if(is_null(DocumentType::where('key',$documentType['key'])->first())){
                DocumentType::create($documentType);

            }
        }
    }
}
