<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Property\app\Models\PropertyType;

class PropertyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $propertyTypes = [
            [
                'name' => [
                    'ar' => 'عمارة',
                    'en' => 'Building',
                ],
                'property_type' => 'property',
                'key' => 'building'
            ],
            [
                'name' => [
                    'ar' => 'فيلا',
                    'en' => 'Villa',
                ],
                'property_type' => 'property',
                'key' => 'villa'
            ],
            [
                'name' => [
                    'ar' => 'مجمع تجاري مفتوح ( بلازا)',
                    'en' => 'Plazza',
                ],
                'property_type' => 'property',
                'key' => 'plazza'
            ],
            [
                'name' => [
                    'ar' => 'أرض',
                    'en' => 'Land',
                ],
                'property_type' => 'property',
                'key' => 'land'
            ],
            [
                'name' => [
                    'ar' => 'مجمع تجاري مغلق (مول)',
                    'en' => 'Shopping mall',
                ],
                'property_type' => 'property',
                'key' => 'shopping_mall'
            ],
            [
                'name' => [
                    'ar' => 'برج',
                    'en' => 'Tower',
                ],
                'property_type' => 'property',
                'key' => 'tower'
            ],
            [
                'name' => [
                    'ar' => 'مصنع',
                    'en' => 'Factory',
                ],
                'property_type' => 'property',
                'key' => 'factory'
            ],
            [
                'name' => [
                    'ar' => 'استراحة',
                    'en' => 'Rest House',
                ],
                'property_type' => 'property',
                'key' => 'rest_house'
            ],
            [
                'name' => [
                    'ar' => 'مزرعة',
                    'en' => 'Farm',
                ],
                'property_type' => 'property',
                'key' => 'farm'
            ],
            [
                'name' => [
                    'ar' => 'فندق',
                    'en' => 'Hotel',
                ],
                'property_type' => 'property',
                'key' => 'hotel'
            ],
            [
                'name' => [
                    'ar' => 'مواقف سيارات',
                    'en' => 'Car Parking',
                ],
                'property_type' => 'unit',
                'key' => 'car_parking'
            ],
            [
                'name' => [
                    'ar' => 'دور',
                    'en' => 'Floor',
                ],
                'property_type' => 'unit',
                'key' => 'floor'
            ],
            [
                'name' => [
                    'ar' => 'شقّة',
                    'en' => 'Apartment',
                ],
                'property_type' => 'unit',
                'key' => 'apartment'
            ],
            [
                'name' => [
                    'ar' => 'شقّة ثنائية الدّور ( دوبلكس )',
                    'en' => 'Duplex',
                ],
                'property_type' => 'unit',
                'key' => 'duplex'
            ],
            [
                'name' => [
                    'ar' => 'شقّة صغيرة ( استوديو)',
                    'en' => 'Studio',
                ],
                'property_type' => 'unit',
                'key' => 'studio'
            ],
            [
                'name' => [
                    'ar' => 'شقّة ملحق',
                    'en' => 'Secondary apartment',
                ],
                'property_type' => 'unit',
                'key' => 'secondary_apartment'
            ],
            [
                'name' => [
                    'ar' => 'شقّة وملحق علوي',
                    'en' => 'Apartment & Extension',
                ],
                'property_type' => 'unit',
                'key' => 'apartment_extension'
            ],
            [
                'name' => [
                    'ar' => 'دور وملحق علوي',
                    'en' => 'Floor & Extension',
                ],
                'property_type' => 'unit',
                'key' => 'floor_extension'
            ],
            [
                'name' => [
                    'ar' => 'فيلا سطح',
                    'en' => 'Penthouse Villa',
                ],
                'property_type' => 'unit',
                'key' => 'penthouse_villa'
            ],
            [
                'name' => [
                    'ar' => 'غرفة سائق',
                    'en' => 'Driver\'s Room',
                ],
                'property_type' => 'unit',
                'key' => 'drivers_room'
            ],
            [
                'name' => [
                    'ar' => 'كشك',
                    'en' => 'Kiosk',
                ],
                'property_type' => 'unit',
                'key' => 'kiosk'
            ],
            [
                'name' => [
                    'ar' => 'محل',
                    'en' => 'Shop',
                ],
                'property_type' => 'unit',
                'key' => 'shop'
            ],
            [
                'name' => [
                    'ar' => 'ورشة',
                    'en' => 'Workshop',
                ],
                'property_type' => 'unit',
                'key' => 'workshop'
            ],
            [
                'name' => [
                    'ar' => 'أرض مسورة',
                    'en' => 'Fenced land lot',
                ],
                'property_type' => 'unit',
                'key' => 'fenced_land_lot'
            ],
            [
                'name' => [
                    'ar' => 'محطة',
                    'en' => 'Station',
                ],
                'property_type' => 'unit',
                'key' => 'station'
            ],
            [
                'name' => [
                    'ar' => 'مكتب',
                    'en' => 'Office space',
                ],
                'property_type' => 'unit',
                'key' => 'office_space'
            ],
            [
                'name' => [
                    'ar' => 'مستودع',
                    'en' => 'Warehouse',
                ],
                'property_type' => 'unit',
                'key' => 'warehouse'
            ],
            [
                'name' => [
                    'ar' => 'معرض',
                    'en' => 'Trade Exhibition',
                ],
                'property_type' => 'unit',
                'key' => 'trade_exhibition'
            ],
            [
                'name' => [
                    'ar' => 'صراف',
                    'en' => 'ATM',
                ],
                'property_type' => 'unit',
                'key' => 'atm'
            ],
            [
                'name' => [
                    'ar' => 'سينما',
                    'en' => 'Cinema',
                ],
                'property_type' => 'unit',
                'key' => 'cinema'
            ],
            [
                'name' => [
                    'ar' => 'محطة كهرباء',
                    'en' => 'Power Substation',
                ],
                'property_type' => 'unit',
                'key' => 'power_substation'
            ],
            [
                'name' => [
                    'ar' => 'برج اتصالات',
                    'en' => 'Telecom Tower',
                ],
                'property_type' => 'unit',
                'key' => 'telecom_tower'
            ],
            [
                'name' => [
                    'ar' => 'مجمع تجاري مفتوح ( بلازا)',
                    'en' => 'Plaza',
                ],
                'property_type' => 'unit',
                'key' => 'plaza'
            ],
            [
                'name' => [
                    'ar' => 'اخرى',
                    'en' => 'Other',
                ],
                'property_type' => 'property',
                'key' => 'other_property_type'
            ],
            [
                'name' => [
                    'ar' => 'اخرى',
                    'en' => 'Other',
                ],
                'property_type' => 'unit',
                'key' => 'other_unit_type'
            ],
        ];

        foreach ($propertyTypes as $propertyTypeData) {
            $propertyType = PropertyType::firstOrNew([
                'name->en' => $propertyTypeData['name']['en'],
                'property_type' => $propertyTypeData['property_type']
            ]);

            $propertyType->name = $propertyTypeData['name'];
            $propertyType->key = $propertyTypeData['key'];
            $propertyType->property_type = $propertyTypeData['property_type'];
            $propertyType->save();
        }
    }
}
