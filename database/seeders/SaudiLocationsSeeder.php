<?php

namespace Database\Seeders;

use App\Imports\SaudiLocationsImport;
use App\Models\Region;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class SaudiLocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if data already exists in the database
        if (Region::count() > 0) {
            $this->command->info('Saudi regions data already exists in the database. Skipping import.');
            return;
        }

        $filePath = database_path('migrations/data/MDM_Locations.xlsx');

        if (File::exists($filePath)) {
            $this->command->info('Importing Saudi regions, cities, and districts from Excel file...');
            Excel::import(new SaudiLocationsImport, $filePath);
            $this->command->info('Saudi regions import completed successfully.');
        } else {
            $this->command->error('Excel file not found at: ' . $filePath);
            Log::error('Saudi regions Excel file not found during seeding at: ' . $filePath);
        }
    }
}
