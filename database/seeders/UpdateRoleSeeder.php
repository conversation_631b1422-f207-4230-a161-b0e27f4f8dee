<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Enums\RoleEnum;

class UpdateRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => RoleEnum::BROKER,
                'guard_name' => 'web',
                'translated_name' => json_encode([
                    'en' => 'Broker',
                    'ar' => 'وسيط'
                ]),
            ],
            [
                'name' => RoleEnum::OWNER,
                'guard_name' => 'web',
                'translated_name' => json_encode([
                    'en' => 'Company Owner',
                    'ar' => 'مالك الشركة'
                ]),
            ],
            [
                'name' => RoleEnum::ADMIN,
                'guard_name' => 'web',
                'translated_name' => json_encode([
                    'en' => 'super admin',
                    'ar' => 'المسئول الاول'
                ]),
            ],
            [
                'name' => RoleEnum::ACCOUNTANT,
                'guard_name' => 'web',
                'translated_name' => json_encode([
                    'en' => 'Accountant',
                    'ar' => 'المحاسب'
                ]),
            ],
        ];

        foreach ($roles as $role) {
            $existing = DB::table('roles')->where('name', $role['name'])->first();

            if ($existing) {
                if (empty($existing->translated_name)) {
                    DB::table('roles')
                        ->where('name', $role['name'])
                        ->update(['translated_name' => $role['translated_name']]);
                }
            } else {
                // If the role does not exist, insert it.
                DB::table('roles')->insert($role);
            }
        }
    }
}
