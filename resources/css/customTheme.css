@import './app.css';
@import url("https://fonts.googleapis.com/css?family=IBM Plex Sans Arabic:300,300i,400,400i,700,700i&display=swap");

.collapse-sidebar-button {
    @apply absolute ltr:right-auto ltr:left-[8px] rtl:left-auto rtl:right-[8px] bg-[#f4f4f4] dark:bg-[#242426] w-[25px] h-[25px]  !important;
}

.uncollapse-sidebar-button {
    @apply absolute ltr:right-auto ltr:left-0 rtl:left-auto rtl:right-0 bg-[#f4f4f4] dark:bg-[#242426] w-[25px] h-[25px] !important;
}

.mainEl{
    @apply min-h-[calc(100vh_-_64px)] max-h-[calc(100vh_-_64px)] overflow-y-auto lg:shadow-[inset_0px_0px_6px_0px_#000000] lg:dark:shadow-[inset_3px_3px_7px_-2px_#8786867d] rtl:lg:dark:shadow-[inset_-3px_3px_7px_-2px_#8786867d] pb-[70px];
}

.custom-background-simple-layout{
    @apply bg-[url('../../images/keraLoginBg.webp')] bg-no-repeat bg-cover bg-center;
}

.close-sidebar-button{
    @apply absolute ltr:right-[24px] ltr:left-auto rtl:left-[24px] rtl:right-auto bg-[#f4f4f4] dark:bg-[#242426] w-[30px] h-[30px] !important;
}

.open-mobile-sidebar-button{
    @apply bg-[#f4f4f4] dark:bg-[#242426] w-[30px] h-[30px] !important;
}

.mainEl>div{
    @apply relative;
}

.radio-deck-cards-color{
    @apply peer-checked:bg-[#0f2c24]  peer-checked:[&>.radio-deck-options-color>span]:text-white peer-checked:[&>.radio-deck-options-color>.radio-deck-options-description-color]:text-[#ffffffa1] !important;
}

.checkbox-list-wrapper-units label.fi-fo-checkbox-list-option-label{
    @apply p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 relative h-full;
}

.checkbox-list-wrapper-units label.fi-fo-checkbox-list-option-label input.fi-checkbox-input{
    @apply m-0 absolute ltr:right-[7px] rtl:left-[7px] top-[7px];
}

.checkbox-list-wrapper-units label.fi-fo-checkbox-list-option-label>div{
    @apply w-full;
}

.checkbox-list-wrapper-units label.fi-fo-checkbox-list-option-label:has(input.fi-checkbox-input:checked) {
    @apply border-primary-600 dark:border-primary-500;
}

.lease-custom-section{
    @apply relative;
}

.lease-custom-section .lease-custom-toggle{
    @apply p-0 absolute top-1/2 ltr:right-0 rtl:left-0 -translate-y-1/2 ltr:-translate-x-6 rtl:translate-x-6;
}

.lease-custom-section .fi-section-content>div.fi-fo-component-ctn{
    @apply gap-0;
}

.lease-custom-section .fi-section-content{
    @apply py-0;
}

.lease-custom-section .fi-section-content-ctn{
    @apply border-t-0;
}

.lease-custom-section .fi-section-header{
    @apply ltr:pr-[72px] rtl:pl-[72px];
}

html[dir="rtl"] {
    font-family: "IBM Plex Sans Arabic";
}

.infolist-tabs{
    @apply lg:px-0 lg:py-0 !important;
}

.plaintext{
    unicode-bidi: plaintext;
}

.custom-action-btn{
    @apply text-[#31846C] dark:text-primary-400 ltr:pr-0 rtl:pl-0 shadow-none !important;
}

.custom-action-btn svg{
    @apply text-[#31846C] dark:text-primary-400 !important;
}

.custom-conditions-section{
    @apply ring-0 bg-transparent shadow-none !important;
}

.custom-conditions-section>div>.fi-section-content{
    @apply p-0 !important;
}

.custom-property-section{
    @apply ring-0 bg-transparent shadow-none !important;
}

.custom-property-section>div>.fi-section-content{
    @apply p-0 !important;
}

.custom_widget_page .fi-wi-stats-overview-header-heading {
    @apply hidden
}

.custom_widget_page .fi-wi-stats-overview {
    @apply gap-y-0
}

.owner_lease_list ul li{
    @apply h-full
}
