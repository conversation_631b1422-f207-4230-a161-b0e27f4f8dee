@php
$id = $getId();
$statePath = $getStatePath();
$isDisabled = $isDisabled();
$maxDate = $getMaxDate();
$minDate = $getMinDate();
$showSwitcher = $getShowSwitcher();
$syncGroup = $getSyncGroup();
$isHijri = $getIsHijri();

@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :id="$id"
    :label="$getLabel()"
    :helper-text="$getHelperText()"
    :hint="$getHint()"
    :hint-icon="$getHintIcon()"
    :required="$isRequired()"
    :state-path="$statePath">
    <x-filament::input.wrapper
    :disabled="$isDisabled"
    >

        <div
            class="relative"
            wire:ignore 
            x-data="{
                state: $wire.entangle('{{ $statePath }}'),
                dateDisplay: '',
                minDateValue: '{{ $minDate }}',
                maxDateValue: '{{ $maxDate }}',
                picker: null,
                syncGroup: '{{ $syncGroup }}',
                isHijri: {{ $isHijri ? 'true' : 'false' }},
                updateDisplayFromState() {
                    if (this.state) {
                        try {
                            let isHijri = this.picker && this.picker.data('HijriDatePicker') ? this.picker.data('HijriDatePicker').hijri() : false;
                            $wire.$set('{{ $statePath }}_isHijri', isHijri);
                            if(isHijri){
                                const gregorianDate = moment(this.state, 'YYYY-MM-DD');
                                const showDate = gregorianDate.format('iYYYY-iMM-iDD');
                                this.dateDisplay = showDate;
                                if (this.picker && this.picker.data('HijriDatePicker')) {
                                    this.picker.data('HijriDatePicker').date(gregorianDate);
                                }
                                $('#{{ str_replace(".", "\\\\." , $id) }}_display').val(showDate);
                            }else{
                                const gregorianDate = moment(this.state, 'YYYY-MM-DD');
                                const showDate = gregorianDate.format('YYYY-MM-DD');
                                this.dateDisplay = showDate;
                                if (this.picker && this.picker.data('HijriDatePicker')) {
                                    this.picker.data('HijriDatePicker').date(gregorianDate);
                                }
                                $('#{{ str_replace(".", "\\\\." , $id) }}_display').val(showDate);
                            }
                        } catch (e) {
                            console.error('Error converting date:', e);
                        }
                    } else {
                        this.dateDisplay = '';
                        $('#{{ str_replace(".", "\\\\." , $id) }}_display').val('');
                        if (this.picker && this.picker.data('HijriDatePicker')) {
                            this.picker.data('HijriDatePicker').clear();
                        }
                    }
                }
            }"
            x-init="(() => {
                if (!window.jQuery) {
                    const jqueryScript = document.createElement('script');
                    jqueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
                    jqueryScript.onload = function() {
                        loadDependencies();
                    };
                    document.head.appendChild(jqueryScript);
                } else {
                    loadDependencies();
                }
                
                function loadDependencies() {
                    const momentScript = document.createElement('script');
                    momentScript.src = '{{ asset("/app/Forms/Components/datepicker/js/moment-with-locales.js") }}';
                    momentScript.onload = function() {
                        const momentHijriScript = document.createElement('script');
                        momentHijriScript.src = '{{ asset("/app/Forms/Components/datepicker/js/moment-hijri.js") }}';
                        momentHijriScript.onload = function() {
                            const datepickerScript = document.createElement('script');
                            datepickerScript.src = '{{ asset("/app/Forms/Components/datepicker/js/bootstrap-hijri-datetimepicker.js") }}';
                            datepickerScript.onload = function() {
                                $(document).ready(function() {
                                    const options = {
                                        timeZone:'Asia/Riyadh',
                                        locale: '{{ config('app.locale')."-sa" }}',
                                        format: 'YYYY-MM-DD',
                                        hijriFormat:'iYYYY-iMM-iDD',
                                        dayViewHeaderFormat: 'MMMM YYYY',
                                        collapse: true,
                                        hijriDayViewHeaderFormat: 'iMMMM iYYYY',
                                        showSwitcher: {{ $showSwitcher ? 'true' : 'false' }},
                                        allowInputToggle: true,
                                        useCurrent: false,
                                        isRTL:'{{ config('app.locale') == "ar" ? true : false }}',
                                        viewMode:'days',
                                        keepOpen: false,
                                        hijri: isHijri,
                                        debug: false,
                                        showClear: true,
                                        showTodayButton: false,
                                        showClose: true,
                                        sideBySide: false,
                                        collapse: false,
                                        hijriText:'{{ __('Hijri') }}',
                                        gregorianText:'{{ __('Gregorian') }}',
                                        icons:{
                                            clear:'{{ __('clear date picker') }}',
                                            close:'{{ __('close date picker') }}',
                                        }
                                        
                                    }
                                    if (minDateValue) {
                                        options.minDate = moment(minDateValue);
                                    }
                                    
                                    if (maxDateValue) {
                                        const formatMaxDateValue = moment(maxDateValue);
                                        options.maxDate = formatMaxDateValue;
                                        options.viewDate = maxDateValue ? formatMaxDateValue : false
                                       
                                    }
                                    
                                    picker = $('#{{ str_replace(".", "\\\\." , $id) }}_display').hijriDatePicker(options);
                                    
                                    updateDisplayFromState();
                                    
                                    $('#{{ str_replace(".", "\\\\." , $id) }}_display').on('dp.change', function(e) {
                                        if (e.date) {
                                            const isHijri = picker.data('HijriDatePicker').hijri();
                                            $wire.$set('{{ $statePath }}_isHijri', isHijri);
                                            const formattedDate = e.date.format('YYYY-MM-DD');
                                            state = formattedDate;
                                            $('#{{ str_replace(".", "\\\\." , $id) }}').val(formattedDate);
                                            
                                            $wire.$set('{{ $statePath }}', formattedDate);
                                        } else {
                                            dateDisplay = '';
                                            state = null;
                                            $('#{{ str_replace(".", "\\\\." , $id) }}').val('');
                                            
                                            $wire.$set('{{ $statePath }}', null);
                                        }
                                    });
    
                                    $('#{{ str_replace(".", "\\\\." , $id) }}_display').on('dp.switchCalendar', function(e) {
                                        const isHijri = picker.data('HijriDatePicker').hijri();
                                        $wire.$set('{{ $statePath }}_isHijri', isHijri);
                                        if (syncGroup) {
                                            $wire.dispatch('syncHijriCalendarType', {
                                                group: syncGroup,
                                                isHijri: isHijri,
                                                source: '{{ $id }}'
                                            });
                                        }
                                    })
    
                                    
                                });
                            };
                            document.head.appendChild(datepickerScript);
                        };
                        document.head.appendChild(momentHijriScript);
                    };
                    document.head.appendChild(momentScript);
                    
                    const datepickerCss = document.createElement('link');
                    datepickerCss.rel = 'stylesheet';
                    datepickerCss.href = '{{ asset("/app/Forms/Components/datepicker/css/bootstrap-datetimepicker.css") }}';
                    document.head.appendChild(datepickerCss);
                }
            })();
            
            $watch('state', function (value) {
                updateDisplayFromState();
            });
            $wire.$on('updateHijriDateConstraints', function(data) {
                let pickerData = picker.data('HijriDatePicker')
                if (data[0].field === '{{ $statePath }}') {
                    if ('minDate' in data[0]) {
                        minDateValue = data[0].minDate ? moment(data[0].minDate,'YYYY-MM-DD') : moment('1950-01-01','YYYY-MM-DD');
                        if (picker) {
                            const hasValue = state && state.length > 0;
                            pickerData.options({
                                minDate: minDateValue,
                               ...((!('maxDate' in data[0]) || !data[0].maxDate) && data[0].minDate ? { viewDate: minDateValue } : {})
                            });
                        }
                    }
                    if ('maxDate' in data[0]) { 
                        maxDateValue = data[0].maxDate ? moment(data[0].maxDate,'YYYY-MM-DD') : moment('2070-01-01','YYYY-MM-DD');
                        if (picker) {
                            const hasValue = state && state.length > 0;
                            picker.data('HijriDatePicker').options({
                                maxDate: maxDateValue,
                                ...(data[0].maxDate ? { viewDate: maxDateValue } : {})
                            });
                        }
                    }
                }
            });
    
            $wire.$on('syncHijriCalendarType', function(data) {
                if (data.group === syncGroup && data.source !== '{{ $id }}' && picker) {
                    picker.data('HijriDatePicker').hijri(data.isHijri);
                    updateDisplayFromState();
                }
            });
            ">
            <input
                type="hidden"
                id="{{ $id }}"
                x-bind:value="state" />
    
            <input
                autocomplete="off"
                x-ref="input"
                id="{{ $id }}_display"
                class="fi-fo-date-time-picker-display-text-input w-full border-none bg-transparent px-3 py-1.5 text-base text-gray-950 outline-none transition duration-75 placeholder:text-gray-400 focus:ring-0 disabled:text-gray-500 disabled:[-webkit-text-fill-color:theme(colors.gray.500)] dark:text-white dark:placeholder:text-gray-500 dark:disabled:text-gray-400 dark:disabled:[-webkit-text-fill-color:theme(colors.gray.400)] sm:text-sm sm:leading-6"
                {{ $isDisabled ? 'disabled' : '' }}
                {!! $isAutofocused() ? 'autofocus' : '' !!}
                placeholder="{{ $getPlaceholder() }}"
                x-bind:value="dateDisplay" />
            </div>
        </x-filament::input.wrapper>

        <span class="fi-fo-field-wrp-helper-text break-words text-sm text-gray-500" 
        x-show="$wire.get('{{ $statePath }}') && {{ $getShowConvertedDate() ? 'true' : 'false' }}" 
        x-data="{ 
            get isHijriMode() { return $wire.get('{{ $statePath }}_isHijri') },
            get formattedDateHjri() {
                const dateValue = $wire.get('{{ $statePath }}');
                if (!dateValue || typeof moment === 'undefined') return '';
                
                try {
                    return moment(dateValue, 'YYYY-MM-DD').format('iYYYY-iMM-iDD');
                } catch (e) {
                    console.error('Error formatting date:', e);
                    return dateValue; // Fallback to raw value
                }
            },
            get formattedDate() {
                const dateValue = $wire.get('{{ $statePath }}');
                if (!dateValue || typeof moment === 'undefined') return '';
                
                try {
                    return moment(dateValue, 'YYYY-MM-DD').format('YYYY-MM-DD');
                } catch (e) {
                    console.error('Error formatting date:', e);
                    return dateValue; // Fallback to raw value
                }
            }
        }">
            <span x-show="isHijriMode && formattedDate">
                {{ __('Gregorian date: ') }}<span x-text="formattedDate" class="font-medium"></span>
            </span>
            <span x-show="!isHijriMode && formattedDateHjri">
            {{ __('Hijri date: ') }} <span x-text="formattedDateHjri" class="font-medium"></span>
            </span>
        </span>

</x-dynamic-component>