<x-filament-panels::page.simple>
    @if (filament()->hasLogin())
        <x-slot name="subheading">
            {{ $this->loginAction }}
        </x-slot>
    @endif

    <x-filament-panels::form wire:submit="request">
        {{ $this->form }}

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />

        @if($email)
            <div class="mt-4">
                @if($attemptCount >= 4)
                    <p class="text-sm text-red-600">{{__('Maximum attempts reached. Please try again in 24 hours.')}}</p>
                @elseif($remainingTime > 0)
                    <p class="text-sm text-gray-600">
                        Please wait <span x-data="{ time: {{ $remainingTime }} }"
                                          x-init="setInterval(() => {
                                if(time > 0) time--;
                                if(time === 0) $wire.checkResendStatus();
                            }, 1000)"
                                          x-text="Math.floor(time / 60) + ':' + (time % 60).toString().padStart(2, '0')"></span>
                        {{__(' before requesting another reset password')}}
                    </p>
                @endif
            </div>
        @endif
    </x-filament-panels::form>
</x-filament-panels::page.simple>

@push('scripts')
    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('countdown-complete', () => {
            @this.checkResendStatus();
            });
        });
    </script>
@endpush
